<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#FFFFFF"/>
  
  <!-- 标题区域 -->
  <rect x="0" y="0" width="1920" height="200" fill="#2E5BBA" opacity="0.1"/>
  
  <!-- 主标题 -->
  <text x="960" y="80" text-anchor="middle" font-family="Microsoft YaHei" font-size="48" font-weight="bold" fill="#2E5BBA">
    【案例010】"跨界"的想象力
  </text>
  <text x="960" y="140" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" fill="#2E5BBA">
    与本地驾校/房产中介的共赢获客模型
  </text>
  
  <!-- 引子部分 -->
  <text x="120" y="280" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#2E5BBA">引子</text>
  <rect x="120" y="300" width="80" height="4" fill="#2E5BBA"/>
  
  <text x="120" y="360" font-family="Microsoft YaHei" font-size="24" fill="#333333">
    "我们自己的营业厅、合作的手机卖场，客户都快被我们'洗'了八遍了，还能去哪里找新客户？"
  </text>
  <text x="120" y="400" font-family="Microsoft YaHei" font-size="24" fill="#333333">
    在一次拉新工作复盘会上，渠道经理老刘的疑问，代表了所有人的心声。
  </text>
  
  <!-- 案主档案 -->
  <text x="120" y="500" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#2E5BBA">案主档案</text>
  <rect x="120" y="520" width="100" height="4" fill="#2E5BBA"/>
  
  <text x="120" y="580" font-family="Microsoft YaHei" font-size="24" fill="#333333">
    老刘，某电信公司渠道合作负责人，深耕渠道多年，但面对日益饱和的传统渠道，感到增长乏力。
  </text>
  
  <!-- 面临的困局 -->
  <text x="120" y="680" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#2E5BBA">面临的"困局"</text>
  <rect x="120" y="700" width="140" height="4" fill="#2E5BBA"/>
  
  <text x="120" y="760" font-family="Microsoft YaHei" font-size="24" fill="#333333">
    老刘发现，无论是在自己的厅店，还是合作的社会渠道，获客成本都越来越高。传统的渠道模式，
  </text>
  <text x="120" y="800" font-family="Microsoft YaHei" font-size="24" fill="#333333">
    已经触碰到了天花板。他迫切需要找到一片全新的、尚未被过度开发的"新大陆"，
  </text>
  <text x="120" y="840" font-family="Microsoft YaHei" font-size="24" fill="#333333">
    去寻找他的潜在客户。
  </text>
  
  <!-- 装饰元素 -->
  <circle cx="1600" cy="400" r="150" fill="#2E5BBA" opacity="0.1"/>
  <circle cx="1650" cy="600" r="80" fill="#2E5BBA" opacity="0.15"/>
  <circle cx="1550" cy="750" r="60" fill="#2E5BBA" opacity="0.2"/>
</svg>
