<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#FFFFFF"/>
  
  <!-- 标题区域 -->
  <rect x="0" y="0" width="1920" height="120" fill="#2E5BBA" opacity="0.1"/>
  
  <!-- 页面标题 -->
  <text x="960" y="70" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#2E5BBA">
    选题策略与"通信+生活"创意结合
  </text>
  
  <!-- 第二步 -->
  <rect x="120" y="160" width="1680" height="120" fill="#F8F9FA" stroke="#2E5BBA" stroke-width="2"/>
  <text x="150" y="200" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#2E5BBA">
    第二步：用新方法找"选题"
  </text>
  <text x="150" y="240" font-family="Microsoft YaHei" font-size="22" fill="#333333">
    我们不再去公司官网找新闻稿，而是用工具，去分析本地最火的几个"吃喝玩乐"博主的爆款视频
  </text>
  
  <!-- AI分析结果 -->
  <rect x="120" y="320" width="1680" height="160" fill="#E8F4FD" stroke="#2E5BBA" stroke-width="1"/>
  <text x="150" y="360" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#2E5BBA">AI分析结果：XX（城市名）本地用户最喜欢看的5类内容</text>
  <text x="150" y="400" font-family="Microsoft YaHei" font-size="20" fill="#333333">
    • 深夜路边摊　　• 周末免费遛娃地　　• 本地人才懂的方言梗
  </text>
  <text x="150" y="440" font-family="Microsoft YaHei" font-size="20" fill="#333333">
    • 性价比美食探店　　• 城市隐藏景点打卡
  </text>
  <text x="150" y="470" font-family="Microsoft YaHei" font-size="18" fill="#666666">
    基于数据分析，精准把握本地用户兴趣点
  </text>
  
  <!-- 第三步 -->
  <rect x="120" y="520" width="1680" height="120" fill="#F8F9FA" stroke="#2E5BBA" stroke-width="2"/>
  <text x="150" y="560" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#2E5BBA">
    第三步：做"通信+生活"的创意结合
  </text>
  <text x="150" y="600" font-family="Microsoft YaHei" font-size="22" fill="#333333">
    我们围绕这些热门选题，去创作内容，并在其中，巧妙地"植入"我们的通信价值
  </text>
  
  <!-- 两个创意示例 -->
  <rect x="120" y="680" width="800" height="280" fill="#E8F4FD" stroke="#2E5BBA" stroke-width="1"/>
  <text x="140" y="720" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#2E5BBA">拍"深夜路边摊"</text>
  <text x="140" y="760" font-family="Microsoft YaHei" font-size="18" fill="#333333">我们去全城最有名的几个烧烤大排档，拍诱人的美食。</text>
  <text x="140" y="790" font-family="Microsoft YaHei" font-size="18" fill="#333333">但在视频的结尾，主播会加一句：</text>
  <text x="140" y="830" font-family="Microsoft YaHei" font-size="16" fill="#2E5BBA">"这家店藏在巷子最深处，信号不好？别担心，</text>
  <text x="140" y="860" font-family="Microsoft YaHei" font-size="16" fill="#2E5BBA">我们移动5G亲测满格！让你一边撸串，一边看</text>
  <text x="140" y="890" font-family="Microsoft YaHei" font-size="16" fill="#2E5BBA">高清欧洲杯直播，毫无压力！"</text>
  <text x="140" y="930" font-family="Microsoft YaHei" font-size="16" fill="#666666">自然植入网络优势，场景化展示价值</text>
  
  <rect x="1000" y="680" width="800" height="280" fill="#E8F4FD" stroke="#2E5BBA" stroke-width="1"/>
  <text x="1020" y="720" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#2E5BBA">拍"周末免费遛娃地"</text>
  <text x="1020" y="760" font-family="Microsoft YaHei" font-size="18" fill="#333333">我们去一个新开放的郊野公园，拍优美的风景。</text>
  <text x="1020" y="790" font-family="Microsoft YaHei" font-size="18" fill="#333333">但在视频里，会有一个镜头是主播拿出测速软件，</text>
  <text x="1020" y="820" font-family="Microsoft YaHei" font-size="18" fill="#333333">展示此处的5G下载速度，并配上字幕：</text>
  <text x="1020" y="860" font-family="Microsoft YaHei" font-size="16" fill="#2E5BBA">"亲测，信号好到可以现场办公，摸鱼带娃两不误！"</text>
  <text x="1020" y="900" font-family="Microsoft YaHei" font-size="16" fill="#666666">专业测评展示，增强可信度</text>
  <text x="1020" y="930" font-family="Microsoft YaHei" font-size="16" fill="#666666">幽默表达，贴近用户心理</text>
</svg>
